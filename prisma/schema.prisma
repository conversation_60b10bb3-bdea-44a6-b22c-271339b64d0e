generator client {
  provider = "prisma-client-js"
}

generator seeder {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id                      String                       @id @default(cuid())
  name                    String?
  email                   String                       @unique
  emailVerified           DateTime?
  password                String?
  image                   String?
  bio                     String?
  location                String?
  followerCount           Int                          @default(0)
  followingCount          Int                          @default(0)
  postCount               Int                          @default(0)
  monthlyArticleLimit     Int                          @default(5)
  articlesReadThisMonth   Int                          @default(0)
  lastArticleResetDate    DateTime?
  createdAt               DateTime                     @default(now())
  updatedAt               DateTime                     @updatedAt
  role                    String                       @default("user")
  twoFactorEnabled        Boolean                      @default(false)
  twoFactorSecret         String?
  backupCodes             String[]
  categories              Category[]
  comments                Comment[]
  commentLikes            CommentLike[]
  following               Follow[]                     @relation("Follower")
  followedBy              Follow[]                     @relation("Following")
  likes                   Like[]
  bookmarks               Bookmark[]
  actions                 Notification[]               @relation("ActionUser")
  notifications           Notification[]               @relation("ReceivedNotifications")
  posts                   Post[]
  readingHistory          ReadingHistory[]
  notificationPreferences UserNotificationPreferences?
  securitySettings        UserSecuritySettings?
  securityEvents          SecurityEvent[]
  subscription            Subscription?
  payments                Payment[]
  articleAccesses         ArticleAccess[]
  passwordResetTokens     PasswordResetToken[]
  emailVerificationTokens EmailVerificationToken[]
  accounts                Account[]
  sessions                Session[]
  apiKeys                 ApiKey[]
}

model Post {
  id                 String           @id @default(cuid())
  title              String
  content            String
  excerpt            String?
  status             String           @default("draft")
  featuredImage      String?
  likeCount          Int              @default(0)
  commentCount       Int              @default(0)
  viewCount          Int              @default(0)
  isPremium          Boolean          @default(false)
  publishedAt        DateTime?
  createdAt          DateTime         @default(now())
  updatedAt          DateTime         @updatedAt
  authorId           String
  readingTime        Int              @default(1)
  scheduledPublishAt DateTime?
  noIndex            Boolean          @default(false)
  ogImage            String?
  seoCanonicalUrl    String?
  seoDescription     String?
  seoKeywords        String?
  seoTitle           String?
  comments           Comment[]
  likes              Like[]
  bookmarks          Bookmark[]
  notifications      Notification[]
  author             User             @relation(fields: [authorId], references: [id], onDelete: Cascade)
  categories         PostCategory[]
  readingHistory     ReadingHistory[]
  articleAccesses    ArticleAccess[]
}

model Category {
  id          String         @id @default(cuid())
  name        String         @unique
  description String?
  isDefault   Boolean        @default(false)
  postCount   Int            @default(0)
  createdAt   DateTime       @default(now())
  updatedAt   DateTime       @updatedAt
  createdById String
  createdBy   User           @relation(fields: [createdById], references: [id])
  posts       PostCategory[]
}

model PostCategory {
  id         String   @id @default(cuid())
  postId     String
  categoryId String
  category   Category @relation(fields: [categoryId], references: [id], onDelete: Cascade)
  post       Post     @relation(fields: [postId], references: [id], onDelete: Cascade)

  @@unique([postId, categoryId])
}

model Comment {
  id            String         @id @default(cuid())
  content       String
  likeCount     Int            @default(0)
  createdAt     DateTime       @default(now())
  updatedAt     DateTime       @updatedAt
  postId        String
  authorId      String
  parentId      String?
  author        User           @relation(fields: [authorId], references: [id], onDelete: Cascade)
  parent        Comment?       @relation("CommentToComment", fields: [parentId], references: [id])
  replies       Comment[]      @relation("CommentToComment")
  post          Post           @relation(fields: [postId], references: [id], onDelete: Cascade)
  likes         CommentLike[]
  notifications Notification[]
}

model Like {
  id        String   @id @default(cuid())
  createdAt DateTime @default(now())
  postId    String
  userId    String
  post      Post     @relation(fields: [postId], references: [id], onDelete: Cascade)
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([postId, userId])
}

model Bookmark {
  id        String   @id @default(cuid())
  createdAt DateTime @default(now())
  postId    String
  userId    String
  post      Post     @relation(fields: [postId], references: [id], onDelete: Cascade)
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([postId, userId])
}

model Follow {
  id          String   @id @default(cuid())
  createdAt   DateTime @default(now())
  followerId  String
  followingId String
  follower    User     @relation("Follower", fields: [followerId], references: [id], onDelete: Cascade)
  following   User     @relation("Following", fields: [followingId], references: [id], onDelete: Cascade)

  @@unique([followerId, followingId])
}

model CommentLike {
  id        String   @id @default(cuid())
  createdAt DateTime @default(now())
  commentId String
  userId    String
  comment   Comment  @relation(fields: [commentId], references: [id], onDelete: Cascade)
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([userId, commentId])
}

model Notification {
  id           String   @id @default(cuid())
  type         String
  read         Boolean  @default(false)
  message      String
  link         String?
  createdAt    DateTime @default(now())
  userId       String
  actionUserId String?
  postId       String?
  commentId    String?
  data         String?
  groupId      String?
  actionUser   User?    @relation("ActionUser", fields: [actionUserId], references: [id])
  comment      Comment? @relation(fields: [commentId], references: [id], onDelete: Cascade)
  post         Post?    @relation(fields: [postId], references: [id], onDelete: Cascade)
  user         User     @relation("ReceivedNotifications", fields: [userId], references: [id], onDelete: Cascade)
}

model ReadingHistory {
  id        String   @id @default(cuid())
  progress  Float    @default(0)
  lastRead  DateTime @default(now())
  createdAt DateTime @default(now())
  userId    String
  postId    String
  completed Boolean  @default(false)
  updatedAt DateTime @updatedAt
  post      Post     @relation(fields: [postId], references: [id], onDelete: Cascade)
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([userId, postId])
  @@index([userId])
  @@index([postId])
  @@index([lastRead])
}

model UserNotificationPreferences {
  id                    String   @id @default(cuid())
  emailNotifications    Boolean  @default(true)
  browserNotifications  Boolean  @default(false)
  newComments           Boolean  @default(true)
  newLikes              Boolean  @default(true)
  newFollowers          Boolean  @default(true)
  mentions              Boolean  @default(true)
  newsletter            Boolean  @default(false)
  marketingEmails       Boolean  @default(false)
  postUpdates           Boolean  @default(true)
  commentReplies        Boolean  @default(true)
  createdAt             DateTime @default(now())
  updatedAt             DateTime @updatedAt
  userId                String   @unique
  mentionsInComments    Boolean  @default(true)
  mentionsInPosts       Boolean  @default(true)
  newPostsFromFollowing Boolean  @default(true)
  user                  User     @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model UserSecuritySettings {
  id                    String   @id @default(cuid())
  emailSecurityAlerts   Boolean  @default(true)
  loginAlerts           Boolean  @default(true)
  passwordChangeAlerts  Boolean  @default(true)
  twoFactorAlerts       Boolean  @default(true)
  suspiciousActivityAlerts Boolean @default(true)
  userId                String   @unique
  user                  User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  createdAt             DateTime @default(now())
  updatedAt             DateTime @updatedAt

  @@map("user_security_settings")
}

model SecurityEvent {
  id          String   @id @default(cuid())
  userId      String
  user        User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  type        String   // 'login', 'password_change', '2fa_setup', '2fa_disable', 'failed_login', 'suspicious_activity'
  description String
  ipAddress   String?
  userAgent   String?
  location    String?  // JSON string with location data
  metadata    String?  // JSON string with additional event data
  emailSent   Boolean  @default(false)
  createdAt   DateTime @default(now())

  @@index([userId])
  @@index([type])
  @@index([createdAt])
  @@map("security_events")
}

// Subscription tiers
enum SubscriptionTier {
  FREE
  MEMBER
}

// Subscription status
enum SubscriptionStatus {
  ACTIVE
  PAST_DUE
  CANCELED
  INCOMPLETE
  INCOMPLETE_EXPIRED
  TRIALING
  UNPAID
}

// Subscription model
model Subscription {
  id                String            @id @default(cuid())
  tier              SubscriptionTier  @default(FREE)
  status            SubscriptionStatus @default(ACTIVE)
  currentPeriodStart DateTime         @default(now())
  currentPeriodEnd  DateTime
  cancelAtPeriodEnd Boolean           @default(false)
  stripeCustomerId  String?
  stripeSubscriptionId String?
  createdAt         DateTime          @default(now())
  updatedAt         DateTime          @updatedAt

  // Relations
  user              User              @relation(fields: [userId], references: [id], onDelete: Cascade)
  userId            String            @unique
  payments          Payment[]
}

// Payment model
model Payment {
  id                  String   @id @default(cuid())
  amount              Int
  currency            String   @default("usd")
  status              String
  stripePaymentIntentId String?
  createdAt           DateTime @default(now())
  updatedAt           DateTime @updatedAt

  // Relations
  user                User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  userId              String
  subscription        Subscription @relation(fields: [subscriptionId], references: [id], onDelete: Cascade)
  subscriptionId      String
}

// ArticleAccess model to track free user article views
model ArticleAccess {
  id          String   @id @default(cuid())
  accessedAt  DateTime @default(now())

  // Relations
  user        User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  userId      String
  post        Post     @relation(fields: [postId], references: [id], onDelete: Cascade)
  postId      String

  @@unique([userId, postId])
}

// Password reset tokens for secure password recovery
model PasswordResetToken {
  id        String   @id @default(cuid())
  token     String   @unique
  userId    String
  expiresAt DateTime
  createdAt DateTime @default(now())
  used      Boolean  @default(false)

  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([token])
  @@index([userId])
  @@index([expiresAt])
}

// Email verification tokens for account confirmation
model EmailVerificationToken {
  id        String   @id @default(cuid())
  token     String   @unique
  userId    String
  email     String
  expiresAt DateTime
  createdAt DateTime @default(now())
  used      Boolean  @default(false)

  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([token])
  @@index([userId])
  @@index([expiresAt])
}

// NextAuth.js Account model for OAuth providers
model Account {
  id                String  @id @default(cuid())
  userId            String
  type              String
  provider          String
  providerAccountId String
  refresh_token     String? @db.Text
  access_token      String? @db.Text
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String? @db.Text
  session_state     String?

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
}

// NextAuth.js Session model
model Session {
  id           String   @id @default(cuid())
  sessionToken String   @unique
  userId       String
  expires      DateTime
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)
}

// NextAuth.js VerificationToken model
model VerificationToken {
  identifier String
  token      String   @unique
  expires    DateTime

  @@unique([identifier, token])
}

// API Keys for programmatic access
model ApiKey {
  id          String   @id @default(cuid())
  name        String   // User-friendly name for the key
  key         String   @unique // The actual API key (hashed)
  keyPrefix   String   // First 8 characters for display (e.g., "jk_12345...")
  permissions String[] @default(["posts:create"]) // Allowed permissions
  lastUsedAt  DateTime?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  expiresAt   DateTime? // Optional expiration
  isActive    Boolean  @default(true)

  // Relations
  user        User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  userId      String

  @@index([key])
  @@index([userId])
  @@index([isActive])
}
