{"name": "journly", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "prisma:generate": "prisma generate", "prisma:migrate": "prisma migrate dev", "prisma:studio": "prisma studio", "db:seed": "npx prisma db seed", "postinstall": "prisma generate", "test": "jest", "test:watch": "jest --watch", "test:unit": "jest --testPathPattern=src/__tests__/unit", "test:integration": "jest --testPathPattern=src/__tests__/integration", "test:e2e": "npx playwright test --project='chromium'", "test:e2e:ui": "playwright test --ui", "test:coverage": "jest --coverage"}, "dependencies": {"@auth/prisma-adapter": "^2.9.1", "@hookform/resolvers": "^5.0.1", "@prisma/client": "^6.9.0", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-hover-card": "^1.1.14", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-tooltip": "^1.2.7", "@stripe/stripe-js": "^7.3.1", "@tanstack/react-query": "^5.79.0", "@tanstack/react-query-devtools": "^5.79.0", "@tiptap/extension-bullet-list": "^2.12.0", "@tiptap/extension-heading": "^2.12.0", "@tiptap/extension-history": "^2.12.0", "@tiptap/extension-image": "^2.12.0", "@tiptap/extension-link": "^2.12.0", "@tiptap/extension-mention": "^2.12.0", "@tiptap/extension-ordered-list": "^2.12.0", "@tiptap/extension-placeholder": "^2.12.0", "@tiptap/extension-youtube": "^2.12.0", "@tiptap/pm": "^2.12.0", "@tiptap/react": "^2.12.0", "@tiptap/starter-kit": "^2.12.0", "@tiptap/suggestion": "^2.12.0", "@types/react-google-recaptcha": "^2.1.9", "@vercel/analytics": "^1.5.0", "@vercel/speed-insights": "^1.2.0", "bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^4.1.0", "lucide-react": "^0.511.0", "next": "15.3.3", "next-auth": "^5.0.0-beta.28", "next-themes": "^0.4.6", "qrcode": "^1.5.4", "react": "^19.1.0", "react-dom": "^19.1.0", "react-google-recaptcha": "^3.1.0", "react-hook-form": "^7.56.4", "resend": "^4.5.1", "shadcn-ui": "^0.9.5", "sonner": "^2.0.4", "speakeasy": "^2.0.0", "stripe": "^18.2.0", "tailwind-merge": "^3.3.0", "ua-parser-js": "^2.0.3", "zod": "^3.25.42"}, "devDependencies": {"@eslint/eslintrc": "^3", "@playwright/test": "^1.52.0", "@tailwindcss/postcss": "^4", "@tailwindcss/typography": "^0.5.16", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/bcryptjs": "^3.0.0", "@types/jest": "^29.5.14", "@types/jest-axe": "^3.5.9", "@types/lodash": "^4.17.17", "@types/node": "^22.15.29", "@types/qrcode": "^1.5.5", "@types/react": "^19.1.6", "@types/react-dom": "^19.1.5", "@types/speakeasy": "^2.0.10", "@types/supertest": "^6.0.3", "@types/ua-parser-js": "^0.7.39", "cloc": "^2.4.0-cloc", "eslint": "^9", "eslint-config-next": "15.3.3", "identity-obj-proxy": "^3.0.0", "jest": "^29.7.0", "jest-axe": "^10.0.0", "jest-environment-jsdom": "^29.7.0", "jest-watch-typeahead": "^2.2.2", "msw": "^2.8.7", "prisma": "^6.9.0", "supertest": "^7.1.1", "tailwindcss": "^4", "ts-jest": "^29.3.4", "ts-node": "^10.9.2", "tw-animate-css": "^1.3.2", "typescript": "^5"}, "prisma": {"seed": "ts-node --compiler-options {\"module\":\"CommonJS\"} prisma/seed.ts", "binaryTargets": ["native", "rhel-openssl-1.0.x"]}}