import prisma from '@/lib/prisma';
import { UAParser } from 'ua-parser-js';

export interface SessionInfo {
  id: string;
  sessionToken: string;
  userId: string;
  expires: Date;
  createdAt?: Date;
  lastAccessed?: Date;
  ipAddress?: string;
  userAgent?: string;
  deviceInfo?: {
    browser?: string;
    os?: string;
    device?: string;
    isMobile?: boolean;
  };
  location?: {
    country?: string;
    city?: string;
  };
  isCurrent?: boolean;
}

export interface LoginActivity {
  id: string;
  userId: string;
  action: 'login' | 'logout' | 'failed_login' | 'session_expired';
  ipAddress?: string;
  userAgent?: string;
  deviceInfo?: {
    browser?: string;
    os?: string;
    device?: string;
    isMobile?: boolean;
  };
  location?: {
    country?: string;
    city?: string;
  };
  timestamp: Date;
  success: boolean;
  details?: string;
}

/**
 * Parse user agent string to extract device information
 */
export function parseUserAgent(userAgent: string) {
  const parser = new UAParser(userAgent);
  const result = parser.getResult();
  
  return {
    browser: result.browser.name ? `${result.browser.name} ${result.browser.version}` : undefined,
    os: result.os.name ? `${result.os.name} ${result.os.version}` : undefined,
    device: result.device.model || result.device.type || 'Desktop',
    isMobile: result.device.type === 'mobile' || result.device.type === 'tablet',
  };
}

/**
 * Get IP address from request headers
 */
export function getClientIP(request: Request): string | undefined {
  const forwarded = request.headers.get('x-forwarded-for');
  const realIP = request.headers.get('x-real-ip');
  const cfConnectingIP = request.headers.get('cf-connecting-ip');
  
  if (forwarded) {
    return forwarded.split(',')[0].trim();
  }
  
  if (realIP) {
    return realIP;
  }
  
  if (cfConnectingIP) {
    return cfConnectingIP;
  }
  
  return undefined;
}

/**
 * Get all active sessions for a user
 * Note: With JWT strategy, sessions are not stored in database.
 * We can only track the current session from the JWT token.
 */
export async function getUserSessions(userId: string, currentSessionToken?: string): Promise<SessionInfo[]> {
  // With JWT strategy, we don't have database sessions
  // We can only return the current session if a token is provided
  if (!currentSessionToken) {
    return [];
  }

  // For JWT sessions, we create a mock session object representing the current session
  // In a real implementation, you might want to store session metadata separately
  const currentSession: SessionInfo = {
    id: 'current-jwt-session',
    sessionToken: currentSessionToken,
    userId: userId,
    expires: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days from now
    isCurrent: true,
    deviceInfo: {
      browser: 'Current Browser',
      os: 'Current OS',
      device: 'Current Device',
      isMobile: false,
    },
  };

  return [currentSession];
}

/**
 * Revoke a specific session
 * Note: With JWT strategy, we can't revoke individual sessions
 */
// eslint-disable-next-line @typescript-eslint/no-unused-vars
export async function revokeSession(_sessionId: string, _userId: string): Promise<boolean> {
  // With JWT strategy, we can't revoke individual sessions
  // The session will expire naturally when the JWT expires
  console.warn('Session revocation not supported with JWT strategy');
  return false;
}

/**
 * Revoke all sessions except the current one
 * Note: With JWT strategy, there are no "other" sessions to revoke
 */
// eslint-disable-next-line @typescript-eslint/no-unused-vars
export async function revokeAllOtherSessions(_userId: string, _currentSessionToken: string): Promise<number> {
  // With JWT strategy, there are no "other" sessions to revoke
  // Each JWT token is independent and expires naturally
  console.warn('Revoking other sessions not supported with JWT strategy');
  return 0;
}

/**
 * Revoke all sessions for a user
 */
export async function revokeAllSessions(userId: string): Promise<number> {
  try {
    const result = await prisma.session.deleteMany({
      where: {
        userId,
      },
    });
    
    return result.count;
  } catch (error) {
    console.error('Error revoking all sessions:', error);
    return 0;
  }
}

/**
 * Clean up expired sessions
 */
export async function cleanupExpiredSessions(): Promise<number> {
  try {
    const result = await prisma.session.deleteMany({
      where: {
        expires: {
          lt: new Date(),
        },
      },
    });
    
    return result.count;
  } catch (error) {
    console.error('Error cleaning up expired sessions:', error);
    return 0;
  }
}

/**
 * Get session count for a user
 * Note: With JWT strategy, we can only count the current session
 */
// eslint-disable-next-line @typescript-eslint/no-unused-vars
export async function getUserSessionCount(_userId: string): Promise<number> {
  // With JWT strategy, we can only count the current session
  // In a real implementation, you might want to track sessions separately
  return 1; // Always 1 for the current JWT session
}

/**
 * Check if a session exists and is valid
 */
export async function isValidSession(sessionToken: string): Promise<boolean> {
  const session = await prisma.session.findUnique({
    where: {
      sessionToken,
      expires: {
        gt: new Date(),
      },
    },
  });
  
  return !!session;
}

/**
 * Update session last accessed time
 */
export async function updateSessionAccess(): Promise<void> {
  try {
    // Note: Since we're using NextAuth's Session model, we can't add custom fields
    // This would require extending the model or using a separate tracking table
    // For now, we'll rely on NextAuth's built-in session management
  } catch (error) {
    console.error('Error updating session access:', error);
  }
}
