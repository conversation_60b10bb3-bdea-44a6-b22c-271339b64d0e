import prisma from '@/lib/prisma';
import { sendSecurityAlertEmail } from '@/lib/email';

export interface SecurityEventData {
  userId: string;
  type: 'login' | 'password_change' | '2fa_setup' | '2fa_disable' | 'failed_login' | 'suspicious_activity';
  description: string;
  ipAddress?: string;
  userAgent?: string;
  location?: {
    country?: string;
    city?: string;
    region?: string;
  };
  metadata?: Record<string, unknown>;
}

export interface UserSecuritySettings {
  emailSecurityAlerts: boolean;
  loginAlerts: boolean;
  passwordChangeAlerts: boolean;
  twoFactorAlerts: boolean;
  suspiciousActivityAlerts: boolean;
}

/**
 * Get user security settings, creating default settings if they don't exist
 */
export async function getUserSecuritySettings(userId: string): Promise<UserSecuritySettings> {
  let settings = await prisma.userSecuritySettings.findUnique({
    where: { userId },
  });

  // Create default settings if they don't exist
  if (!settings) {
    settings = await prisma.userSecuritySettings.create({
      data: {
        userId,
        emailSecurityAlerts: true,
        loginAlerts: true,
        passwordChangeAlerts: true,
        twoFactorAlerts: true,
        suspiciousActivityAlerts: true,
      },
    });
  }

  return {
    emailSecurityAlerts: settings.emailSecurityAlerts,
    loginAlerts: settings.loginAlerts,
    passwordChangeAlerts: settings.passwordChangeAlerts,
    twoFactorAlerts: settings.twoFactorAlerts,
    suspiciousActivityAlerts: settings.suspiciousActivityAlerts,
  };
}

/**
 * Update user security settings
 */
export async function updateUserSecuritySettings(
  userId: string,
  updates: Partial<UserSecuritySettings>
): Promise<UserSecuritySettings> {
  const settings = await prisma.userSecuritySettings.upsert({
    where: { userId },
    update: updates,
    create: {
      userId,
      emailSecurityAlerts: updates.emailSecurityAlerts ?? true,
      loginAlerts: updates.loginAlerts ?? true,
      passwordChangeAlerts: updates.passwordChangeAlerts ?? true,
      twoFactorAlerts: updates.twoFactorAlerts ?? true,
      suspiciousActivityAlerts: updates.suspiciousActivityAlerts ?? true,
    },
  });

  return {
    emailSecurityAlerts: settings.emailSecurityAlerts,
    loginAlerts: settings.loginAlerts,
    passwordChangeAlerts: settings.passwordChangeAlerts,
    twoFactorAlerts: settings.twoFactorAlerts,
    suspiciousActivityAlerts: settings.suspiciousActivityAlerts,
  };
}

/**
 * Log a security event and optionally send email alert
 */
export async function logSecurityEvent(eventData: SecurityEventData): Promise<void> {
  try {
    // Get user and security settings
    const user = await prisma.user.findUnique({
      where: { id: eventData.userId },
      select: { id: true, name: true, email: true },
    });

    if (!user) {
      console.error('[Security] User not found for security event:', eventData.userId);
      return;
    }

    const settings = await getUserSecuritySettings(eventData.userId);

    // Get location from IP address if not provided and IP is available
    let location = eventData.location;
    if (!location && eventData.ipAddress) {
      try {
        const geoLocation = await getLocationFromIP(eventData.ipAddress);
        location = geoLocation || undefined;
      } catch (error) {
        console.warn('[Security] Failed to get location from IP:', error);
      }
    }

    // Create security event record
    const securityEvent = await prisma.securityEvent.create({
      data: {
        userId: eventData.userId,
        type: eventData.type,
        description: eventData.description,
        ipAddress: eventData.ipAddress,
        userAgent: eventData.userAgent,
        location: location ? JSON.stringify(location) : null,
        metadata: eventData.metadata ? JSON.stringify(eventData.metadata) : null,
        emailSent: false,
      },
    });

    // Check if we should send email alert
    const shouldSendEmail = settings.emailSecurityAlerts && shouldSendEmailForEventType(eventData.type, settings);

    if (shouldSendEmail) {
      try {
        const emailResult = await sendSecurityAlertEmail(
          user.email,
          user.name || undefined,
          eventData
        );

        if (emailResult.success) {
          // Mark email as sent
          await prisma.securityEvent.update({
            where: { id: securityEvent.id },
            data: { emailSent: true },
          });

          console.log('[Security] Security alert email sent:', {
            userId: eventData.userId,
            type: eventData.type,
            emailId: emailResult.id,
          });
        } else {
          console.error('[Security] Failed to send security alert email:', emailResult.error);
        }
      } catch (emailError) {
        console.error('[Security] Error sending security alert email:', emailError);
      }
    }

    console.log('[Security] Security event logged:', {
      userId: eventData.userId,
      type: eventData.type,
      emailSent: shouldSendEmail,
    });
  } catch (error) {
    console.error('[Security] Error logging security event:', error);
  }
}

/**
 * Check if email should be sent for specific event type based on user preferences
 */
function shouldSendEmailForEventType(eventType: SecurityEventData['type'], settings: UserSecuritySettings): boolean {
  switch (eventType) {
    case 'login':
      return settings.loginAlerts;
    case 'password_change':
      return settings.passwordChangeAlerts;
    case '2fa_setup':
    case '2fa_disable':
      return settings.twoFactorAlerts;
    case 'failed_login':
    case 'suspicious_activity':
      return settings.suspiciousActivityAlerts;
    default:
      return false;
  }
}

/**
 * Get recent security events for a user
 */
export async function getUserSecurityEvents(
  userId: string,
  limit: number = 10
): Promise<Array<{
  id: string;
  type: string;
  description: string;
  ipAddress?: string;
  location?: { country?: string; city?: string; region?: string } | null;
  createdAt: Date;
  emailSent: boolean;
}>> {
  const events = await prisma.securityEvent.findMany({
    where: { userId },
    orderBy: { createdAt: 'desc' },
    take: limit,
    select: {
      id: true,
      type: true,
      description: true,
      ipAddress: true,
      location: true,
      createdAt: true,
      emailSent: true,
    },
  });

  return events.map(event => ({
    id: event.id,
    type: event.type,
    description: event.description,
    ipAddress: event.ipAddress || undefined,
    location: event.location ? JSON.parse(event.location) : null,
    createdAt: event.createdAt,
    emailSent: event.emailSent,
  }));
}

/**
 * Helper function to extract location from IP address using ipapi.co
 * Free tier: 1000 requests/day, no API key required
 */
export async function getLocationFromIP(ipAddress: string): Promise<{ country?: string; city?: string; region?: string } | null> {
  // Handle local/development IPs
  if (ipAddress === '127.0.0.1' || ipAddress === '::1' || ipAddress === 'localhost') {
    return { country: 'Local', city: 'Localhost', region: 'Development' };
  }

  // Handle private IP ranges
  if (isPrivateIP(ipAddress)) {
    return { country: 'Private Network', city: 'Local Network', region: 'Private' };
  }

  try {
    // Use ipapi.co free service (1000 requests/day, no API key needed)
    const response = await fetch(`https://ipapi.co/${ipAddress}/json/`, {
      method: 'GET',
      headers: {
        'User-Agent': 'Journly Security Service/1.0',
      },
      // Add timeout to prevent hanging requests
      signal: AbortSignal.timeout(5000), // 5 second timeout
    });

    if (!response.ok) {
      console.warn(`[Security] IP geolocation failed: ${response.status} ${response.statusText}`);
      return null;
    }

    const data = await response.json();

    // Check for API errors
    if (data.error) {
      console.warn(`[Security] IP geolocation error: ${data.reason}`);
      return null;
    }

    // Extract location data
    const location = {
      country: data.country_name || undefined,
      city: data.city || undefined,
      region: data.region || undefined,
    };

    // Only return if we have at least one piece of location data
    if (location.country || location.city || location.region) {
      return location;
    }

    return null;
  } catch (error) {
    // Log error but don't throw - geolocation is not critical
    console.warn('[Security] IP geolocation service error:', error);
    return null;
  }
}

/**
 * Check if an IP address is in a private range
 */
function isPrivateIP(ip: string): boolean {
  // IPv4 private ranges
  const privateRanges = [
    /^10\./,                    // 10.0.0.0/8
    /^172\.(1[6-9]|2[0-9]|3[0-1])\./, // **********/12
    /^192\.168\./,              // ***********/16
    /^169\.254\./,              // ***********/16 (link-local)
    /^127\./,                   // *********/8 (loopback)
  ];

  // IPv6 private/local ranges
  const ipv6PrivateRanges = [
    /^::1$/,                    // IPv6 loopback
    /^fe80:/,                   // IPv6 link-local
    /^fc00:/,                   // IPv6 unique local
    /^fd00:/,                   // IPv6 unique local
  ];

  return privateRanges.some(range => range.test(ip)) ||
         ipv6PrivateRanges.some(range => range.test(ip));
}

/**
 * Helper function to parse user agent
 */
export function parseUserAgent(userAgent: string): { browser?: string; os?: string; device?: string } {
  // Simple user agent parsing - in production you might use a library like ua-parser-js
  const browser = userAgent.includes('Chrome') ? 'Chrome' :
                 userAgent.includes('Firefox') ? 'Firefox' :
                 userAgent.includes('Safari') ? 'Safari' :
                 userAgent.includes('Edge') ? 'Edge' : 'Unknown';

  const os = userAgent.includes('Windows') ? 'Windows' :
            userAgent.includes('Mac') ? 'macOS' :
            userAgent.includes('Linux') ? 'Linux' :
            userAgent.includes('Android') ? 'Android' :
            userAgent.includes('iOS') ? 'iOS' : 'Unknown';

  const device = userAgent.includes('Mobile') ? 'Mobile' : 'Desktop';

  return { browser, os, device };
}
