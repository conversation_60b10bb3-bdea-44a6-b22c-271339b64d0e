import prisma from '@/lib/prisma';
import { sendSecurityAlertEmail } from '@/lib/email';

export interface SecurityEventData {
  userId: string;
  type: 'login' | 'password_change' | '2fa_setup' | '2fa_disable' | 'failed_login' | 'suspicious_activity';
  description: string;
  ipAddress?: string;
  userAgent?: string;
  location?: {
    country?: string;
    city?: string;
    region?: string;
  };
  metadata?: Record<string, unknown>;
}

export interface UserSecuritySettings {
  emailSecurityAlerts: boolean;
  loginAlerts: boolean;
  passwordChangeAlerts: boolean;
  twoFactorAlerts: boolean;
  suspiciousActivityAlerts: boolean;
}

/**
 * Get user security settings, creating default settings if they don't exist
 */
export async function getUserSecuritySettings(userId: string): Promise<UserSecuritySettings> {
  let settings = await prisma.userSecuritySettings.findUnique({
    where: { userId },
  });

  // Create default settings if they don't exist
  if (!settings) {
    settings = await prisma.userSecuritySettings.create({
      data: {
        userId,
        emailSecurityAlerts: true,
        loginAlerts: true,
        passwordChangeAlerts: true,
        twoFactorAlerts: true,
        suspiciousActivityAlerts: true,
      },
    });
  }

  return {
    emailSecurityAlerts: settings.emailSecurityAlerts,
    loginAlerts: settings.loginAlerts,
    passwordChangeAlerts: settings.passwordChangeAlerts,
    twoFactorAlerts: settings.twoFactorAlerts,
    suspiciousActivityAlerts: settings.suspiciousActivityAlerts,
  };
}

/**
 * Update user security settings
 */
export async function updateUserSecuritySettings(
  userId: string,
  updates: Partial<UserSecuritySettings>
): Promise<UserSecuritySettings> {
  const settings = await prisma.userSecuritySettings.upsert({
    where: { userId },
    update: updates,
    create: {
      userId,
      emailSecurityAlerts: updates.emailSecurityAlerts ?? true,
      loginAlerts: updates.loginAlerts ?? true,
      passwordChangeAlerts: updates.passwordChangeAlerts ?? true,
      twoFactorAlerts: updates.twoFactorAlerts ?? true,
      suspiciousActivityAlerts: updates.suspiciousActivityAlerts ?? true,
    },
  });

  return {
    emailSecurityAlerts: settings.emailSecurityAlerts,
    loginAlerts: settings.loginAlerts,
    passwordChangeAlerts: settings.passwordChangeAlerts,
    twoFactorAlerts: settings.twoFactorAlerts,
    suspiciousActivityAlerts: settings.suspiciousActivityAlerts,
  };
}

/**
 * Log a security event and optionally send email alert
 */
export async function logSecurityEvent(eventData: SecurityEventData): Promise<void> {
  try {
    // Get user and security settings
    const user = await prisma.user.findUnique({
      where: { id: eventData.userId },
      select: { id: true, name: true, email: true },
    });

    if (!user) {
      console.error('[Security] User not found for security event:', eventData.userId);
      return;
    }

    const settings = await getUserSecuritySettings(eventData.userId);

    // Create security event record
    const securityEvent = await prisma.securityEvent.create({
      data: {
        userId: eventData.userId,
        type: eventData.type,
        description: eventData.description,
        ipAddress: eventData.ipAddress,
        userAgent: eventData.userAgent,
        location: eventData.location ? JSON.stringify(eventData.location) : null,
        metadata: eventData.metadata ? JSON.stringify(eventData.metadata) : null,
        emailSent: false,
      },
    });

    // Check if we should send email alert
    const shouldSendEmail = settings.emailSecurityAlerts && shouldSendEmailForEventType(eventData.type, settings);

    if (shouldSendEmail) {
      try {
        const emailResult = await sendSecurityAlertEmail(
          user.email,
          user.name || undefined,
          eventData
        );

        if (emailResult.success) {
          // Mark email as sent
          await prisma.securityEvent.update({
            where: { id: securityEvent.id },
            data: { emailSent: true },
          });

          console.log('[Security] Security alert email sent:', {
            userId: eventData.userId,
            type: eventData.type,
            emailId: emailResult.id,
          });
        } else {
          console.error('[Security] Failed to send security alert email:', emailResult.error);
        }
      } catch (emailError) {
        console.error('[Security] Error sending security alert email:', emailError);
      }
    }

    console.log('[Security] Security event logged:', {
      userId: eventData.userId,
      type: eventData.type,
      emailSent: shouldSendEmail,
    });
  } catch (error) {
    console.error('[Security] Error logging security event:', error);
  }
}

/**
 * Check if email should be sent for specific event type based on user preferences
 */
function shouldSendEmailForEventType(eventType: SecurityEventData['type'], settings: UserSecuritySettings): boolean {
  switch (eventType) {
    case 'login':
      return settings.loginAlerts;
    case 'password_change':
      return settings.passwordChangeAlerts;
    case '2fa_setup':
    case '2fa_disable':
      return settings.twoFactorAlerts;
    case 'failed_login':
    case 'suspicious_activity':
      return settings.suspiciousActivityAlerts;
    default:
      return false;
  }
}

/**
 * Get recent security events for a user
 */
export async function getUserSecurityEvents(
  userId: string,
  limit: number = 10
): Promise<Array<{
  id: string;
  type: string;
  description: string;
  ipAddress?: string;
  location?: { country?: string; city?: string; region?: string } | null;
  createdAt: Date;
  emailSent: boolean;
}>> {
  const events = await prisma.securityEvent.findMany({
    where: { userId },
    orderBy: { createdAt: 'desc' },
    take: limit,
    select: {
      id: true,
      type: true,
      description: true,
      ipAddress: true,
      location: true,
      createdAt: true,
      emailSent: true,
    },
  });

  return events.map((event: { ipAddress: string; location: string; }) => ({
    ...event,
    ipAddress: event.ipAddress || undefined,
    location: event.location ? JSON.parse(event.location) : null,
  }));
}

/**
 * Helper function to extract location from IP address
 * In a real implementation, you would use a geolocation service
 */
export function getLocationFromIP(ipAddress: string): { country?: string; city?: string; region?: string } | null {
  // Placeholder implementation
  // In production, you would use a service like MaxMind GeoIP2, ipapi.co, etc.
  if (ipAddress === '127.0.0.1' || ipAddress === '::1') {
    return { country: 'Local', city: 'Localhost', region: 'Development' };
  }
  
  // Return null for now - implement actual geolocation service as needed
  return null;
}

/**
 * Helper function to parse user agent
 */
export function parseUserAgent(userAgent: string): { browser?: string; os?: string; device?: string } {
  // Simple user agent parsing - in production you might use a library like ua-parser-js
  const browser = userAgent.includes('Chrome') ? 'Chrome' :
                 userAgent.includes('Firefox') ? 'Firefox' :
                 userAgent.includes('Safari') ? 'Safari' :
                 userAgent.includes('Edge') ? 'Edge' : 'Unknown';

  const os = userAgent.includes('Windows') ? 'Windows' :
            userAgent.includes('Mac') ? 'macOS' :
            userAgent.includes('Linux') ? 'Linux' :
            userAgent.includes('Android') ? 'Android' :
            userAgent.includes('iOS') ? 'iOS' : 'Unknown';

  const device = userAgent.includes('Mobile') ? 'Mobile' : 'Desktop';

  return { browser, os, device };
}
