import { NextRequest } from 'next/server';

/**
 * Extract the real IP address from a Next.js request
 * Handles various proxy headers and edge cases
 */
export function getClientIP(request: NextRequest): string | undefined {
  // Try various headers in order of preference
  const headers = [
    'x-forwarded-for',
    'x-real-ip',
    'x-client-ip',
    'cf-connecting-ip', // Cloudflare
    'x-forwarded',
    'forwarded-for',
    'forwarded',
  ];

  for (const header of headers) {
    const value = request.headers.get(header);
    if (value) {
      // x-forwarded-for can contain multiple IPs, take the first one
      const ip = value.split(',')[0].trim();
      if (isValidIP(ip)) {
        return ip;
      }
    }
  }

  // No valid IP found in headers
  return undefined;
}

/**
 * Basic IP address validation
 */
function isValidIP(ip: string): boolean {
  // Remove any port numbers
  const cleanIP = ip.split(':')[0];
  
  // IPv4 regex
  const ipv4Regex = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;
  
  // IPv6 regex (simplified)
  const ipv6Regex = /^(?:[0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}$|^::1$|^::$/;
  
  return ipv4Regex.test(cleanIP) || ipv6Regex.test(cleanIP);
}

/**
 * Get user agent from request headers
 */
export function getUserAgent(request: NextRequest): string | undefined {
  return request.headers.get('user-agent') || undefined;
}

/**
 * Extract both IP and user agent from request
 */
export function getRequestMetadata(request: NextRequest): {
  ipAddress?: string;
  userAgent?: string;
} {
  return {
    ipAddress: getClientIP(request),
    userAgent: getUserAgent(request),
  };
}
