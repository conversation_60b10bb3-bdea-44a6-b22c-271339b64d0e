"use client";

import { useState } from 'react';
import { Button } from '@/app/components/ui/button';
import { Input } from '@/app/components/ui/input';
import { Label } from '@/app/components/ui/label';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/app/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/app/components/ui/select';
import { Textarea } from '@/app/components/ui/textarea';
import { Alert, AlertDescription } from '@/app/components/ui/alert';
import { Loader2, Shield, MapPin, AlertTriangle, CheckCircle } from 'lucide-react';

interface SecurityTestPanelProps {
  userRole?: string;
}

interface TestResult {
  success: boolean;
  message?: string;
  eventType?: string;
  userId?: string;
  description?: string;
  ipAddress?: string;
  timestamp?: string;
  testIP?: string;
  detectedIP?: string;
  userAgent?: string;
  location?: {
    country?: string;
    city?: string;
    region?: string;
  } | null;
  adminUser?: string;
}

export function SecurityTestPanel({ userRole }: SecurityTestPanelProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [result, setResult] = useState<TestResult | null>(null);
  const [error, setError] = useState('');

  // Security Event Test State
  const [eventType, setEventType] = useState('');
  const [eventDescription, setEventDescription] = useState('');
  const [targetUserId, setTargetUserId] = useState('');

  // Geolocation Test State
  const [testIP, setTestIP] = useState('');

  // Only show for admin users
  if (userRole !== 'admin') {
    return (
      <Alert>
        <AlertTriangle className="h-4 w-4" />
        <AlertDescription>
          Admin access required to view security testing tools.
        </AlertDescription>
      </Alert>
    );
  }

  const testSecurityEvent = async () => {
    if (!eventType) {
      setError('Please select an event type');
      return;
    }

    setIsLoading(true);
    setError('');
    setResult(null);

    try {
      const response = await fetch('/api/admin/security-event', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          eventType,
          description: eventDescription || undefined,
          targetUserId: targetUserId || undefined,
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to test security event');
      }

      setResult(data);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error');
    } finally {
      setIsLoading(false);
    }
  };

  const testGeolocation = async () => {
    if (!testIP) {
      setError('Please enter an IP address');
      return;
    }

    setIsLoading(true);
    setError('');
    setResult(null);

    try {
      const response = await fetch('/api/admin/geolocation', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ ip: testIP }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to test geolocation');
      }

      setResult(data);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error');
    } finally {
      setIsLoading(false);
    }
  };

  const testCurrentIP = async () => {
    setIsLoading(true);
    setError('');
    setResult(null);

    try {
      const response = await fetch('/api/admin/geolocation');
      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to test current IP');
      }

      setResult(data);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="space-y-6">
      <div className="grid gap-6 md:grid-cols-2">
        {/* Security Event Testing */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Shield className="h-5 w-5" />
              Security Event Testing
            </CardTitle>
            <CardDescription>
              Test security event logging and email alerts
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label htmlFor="eventType">Event Type</Label>
              <Select value={eventType} onValueChange={setEventType}>
                <SelectTrigger>
                  <SelectValue placeholder="Select event type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="login">Login</SelectItem>
                  <SelectItem value="password_change">Password Change</SelectItem>
                  <SelectItem value="2fa_setup">2FA Setup</SelectItem>
                  <SelectItem value="2fa_disable">2FA Disable</SelectItem>
                  <SelectItem value="failed_login">Failed Login</SelectItem>
                  <SelectItem value="suspicious_activity">Suspicious Activity</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="eventDescription">Description (Optional)</Label>
              <Textarea
                id="eventDescription"
                placeholder="Custom event description..."
                value={eventDescription}
                onChange={(e) => setEventDescription(e.target.value)}
                rows={2}
              />
            </div>

            <div>
              <Label htmlFor="targetUserId">Target User ID (Optional)</Label>
              <Input
                id="targetUserId"
                placeholder="User ID to test event for..."
                value={targetUserId}
                onChange={(e) => setTargetUserId(e.target.value)}
              />
            </div>

            <Button 
              onClick={testSecurityEvent} 
              disabled={isLoading || !eventType}
              className="w-full"
            >
              {isLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Testing...
                </>
              ) : (
                'Test Security Event'
              )}
            </Button>
          </CardContent>
        </Card>

        {/* Geolocation Testing */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <MapPin className="h-5 w-5" />
              Geolocation Testing
            </CardTitle>
            <CardDescription>
              Test IP geolocation functionality
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label htmlFor="testIP">IP Address</Label>
              <Input
                id="testIP"
                placeholder="e.g., *******"
                value={testIP}
                onChange={(e) => setTestIP(e.target.value)}
              />
            </div>

            <div className="space-y-2">
              <Button 
                onClick={testGeolocation} 
                disabled={isLoading || !testIP}
                className="w-full"
              >
                {isLoading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Testing...
                  </>
                ) : (
                  'Test IP Geolocation'
                )}
              </Button>

              <Button 
                onClick={testCurrentIP} 
                disabled={isLoading}
                variant="outline"
                className="w-full"
              >
                Test Current IP
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Error Display */}
      {error && (
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {/* Result Display */}
      {result && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <CheckCircle className="h-5 w-5 text-green-600" />
              Test Result
            </CardTitle>
          </CardHeader>
          <CardContent>
            <pre className="bg-gray-100 dark:bg-gray-800 p-4 rounded-md overflow-auto text-sm">
              {JSON.stringify(result, null, 2)}
            </pre>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
