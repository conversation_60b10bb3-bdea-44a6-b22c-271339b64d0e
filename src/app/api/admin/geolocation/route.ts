import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/lib/auth';
import { getLocationFromIP } from '@/lib/services/security-service';
import { getRequestMetadata } from '@/lib/utils/ip-utils';
import { rateLimit, createRateLimitHeaders } from '@/lib/utils/rate-limit';
import { z } from 'zod';

// Validation schema for POST requests
const geolocationTestSchema = z.object({
  ip: z.string().min(1, 'IP address is required'),
});

/**
 * Geolocation testing endpoint (admin only)
 * This endpoint allows administrators to test IP geolocation functionality
 * and verify location detection is working correctly
 */
export async function GET(request: NextRequest) {

  try {
    // Check authentication and admin role
    const session = await auth();
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Only allow admin users to test geolocation
    if (session.user.role !== 'admin') {
      return NextResponse.json(
        { error: 'Admin access required' },
        { status: 403 }
      );
    }

    // Rate limiting: 30 requests per 5 minutes per admin user
    const rateLimitKey = `admin-geolocation:${session.user.id}`;
    const rateLimitResult = rateLimit(rateLimitKey, 30, 5 * 60 * 1000);

    if (!rateLimitResult.success) {
      const headers = createRateLimitHeaders(30, rateLimitResult.remaining, rateLimitResult.resetTime);
      return NextResponse.json(
        { error: 'Rate limit exceeded. Please try again later.' },
        { status: 429, headers }
      );
    }

    const { ipAddress, userAgent } = getRequestMetadata(request);

    // Get IP from query parameter or use detected IP
    const testIP = request.nextUrl.searchParams.get('ip') || ipAddress || '127.0.0.1';

    console.log(`[Geolocation Test] Admin ${session.user.email} testing IP: ${testIP}`);

    // Test geolocation
    const location = await getLocationFromIP(testIP);

    // Add rate limit headers to successful response
    const headers = createRateLimitHeaders(30, rateLimitResult.remaining, rateLimitResult.resetTime);

    return NextResponse.json({
      success: true,
      testIP,
      detectedIP: ipAddress,
      userAgent,
      location,
      adminUser: session.user.email,
      timestamp: new Date().toISOString(),
    }, { headers });

  } catch (error) {
    console.error('[Geolocation Test] Error:', error);
    return NextResponse.json(
      {
        error: 'Failed to test geolocation',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

/**
 * POST endpoint to test specific IP addresses
 */
export async function POST(request: NextRequest) {

  try {
    // Check authentication and admin role
    const session = await auth();
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    if (session.user.role !== 'admin') {
      return NextResponse.json(
        { error: 'Admin access required' },
        { status: 403 }
      );
    }

    // Rate limiting: 30 requests per 5 minutes per admin user
    const rateLimitKey = `admin-geolocation:${session.user.id}`;
    const rateLimitResult = rateLimit(rateLimitKey, 30, 5 * 60 * 1000);

    if (!rateLimitResult.success) {
      const headers = createRateLimitHeaders(30, rateLimitResult.remaining, rateLimitResult.resetTime);
      return NextResponse.json(
        { error: 'Rate limit exceeded. Please try again later.' },
        { status: 429, headers }
      );
    }

    // Validate request body
    const body = await request.json();
    const validationResult = geolocationTestSchema.safeParse(body);

    if (!validationResult.success) {
      return NextResponse.json(
        {
          error: 'Invalid request data',
          details: validationResult.error.errors
        },
        { status: 400 }
      );
    }

    const { ip } = validationResult.data;

    console.log(`[Geolocation Test] Admin ${session.user.email} testing IP: ${ip}`);

    // Test geolocation
    const location = await getLocationFromIP(ip);

    // Add rate limit headers to successful response
    const headers = createRateLimitHeaders(30, rateLimitResult.remaining, rateLimitResult.resetTime);

    return NextResponse.json({
      success: true,
      testIP: ip,
      location,
      adminUser: session.user.email,
      timestamp: new Date().toISOString(),
    }, { headers });

  } catch (error) {
    console.error('[Geolocation Test] POST Error:', error);
    return NextResponse.json(
      {
        error: 'Failed to test geolocation',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
