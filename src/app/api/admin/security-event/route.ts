import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/lib/auth';
import { logSecurityEvent } from '@/lib/services/security-service';
import { getRequestMetadata } from '@/lib/utils/ip-utils';
import { rateLimit, createRateLimitHeaders } from '@/lib/utils/rate-limit';
import { z } from 'zod';

// Validation schema
const securityEventTestSchema = z.object({
  eventType: z.enum([
    'login',
    'password_change',
    '2fa_setup',
    '2fa_disable',
    'failed_login',
    'suspicious_activity'
  ]),
  description: z.string().optional(),
  targetUserId: z.string().optional(), // Allow testing events for other users (admin only)
});

/**
 * Security event testing endpoint (admin only)
 * This endpoint allows administrators to test the security event system
 * and verify email alerts are working correctly
 */
export async function POST(request: NextRequest) {

  try {
    // Check authentication and admin role
    const session = await auth();
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Only allow admin users to test security events
    if (session.user.role !== 'admin') {
      return NextResponse.json(
        { error: 'Admin access required' },
        { status: 403 }
      );
    }

    // Rate limiting: 20 requests per 5 minutes per admin user
    const rateLimitKey = `admin-security-event:${session.user.id}`;
    const rateLimitResult = rateLimit(rateLimitKey, 20, 5 * 60 * 1000);

    if (!rateLimitResult.success) {
      const headers = createRateLimitHeaders(20, rateLimitResult.remaining, rateLimitResult.resetTime);
      return NextResponse.json(
        { error: 'Rate limit exceeded. Please try again later.' },
        { status: 429, headers }
      );
    }

    // Validate request body
    const body = await request.json();
    const validationResult = securityEventTestSchema.safeParse(body);

    if (!validationResult.success) {
      return NextResponse.json(
        {
          error: 'Invalid request data',
          details: validationResult.error.errors
        },
        { status: 400 }
      );
    }

    const { eventType, description, targetUserId } = validationResult.data;

    // Use target user ID if provided (admin testing for other users), otherwise use current user
    const userId = targetUserId || session.user.id;
    const { ipAddress, userAgent } = getRequestMetadata(request);

    // Fallback for testing if no IP detected
    const finalIpAddress = ipAddress || '127.0.0.1';
    const finalUserAgent = userAgent || 'Admin Security Test';

    // Custom description or default based on event type
    const eventDescription = description || `Admin test: ${eventType} security event`;

    // Test different event types
    switch (eventType) {
      case 'login':
        await logSecurityEvent({
          userId,
          type: 'login',
          description: eventDescription,
          ipAddress: finalIpAddress,
          userAgent: finalUserAgent,
        });
        break;

      case 'password_change':
        await logSecurityEvent({
          userId,
          type: 'password_change',
          description: eventDescription,
          ipAddress: finalIpAddress,
          userAgent: finalUserAgent,
        });
        break;

      case '2fa_setup':
        await logSecurityEvent({
          userId,
          type: '2fa_setup',
          description: eventDescription,
          ipAddress: finalIpAddress,
          userAgent: finalUserAgent,
        });
        break;

      case '2fa_disable':
        await logSecurityEvent({
          userId,
          type: '2fa_disable',
          description: eventDescription,
          ipAddress: finalIpAddress,
          userAgent: finalUserAgent,
        });
        break;

      case 'failed_login':
        await logSecurityEvent({
          userId,
          type: 'failed_login',
          description: eventDescription,
          ipAddress: finalIpAddress,
          userAgent: finalUserAgent,
        });
        break;

      case 'suspicious_activity':
        await logSecurityEvent({
          userId,
          type: 'suspicious_activity',
          description: eventDescription,
          ipAddress: finalIpAddress,
          userAgent: finalUserAgent,
          // Location will be automatically resolved from IP address
        });
        break;

      default:
        return NextResponse.json(
          { error: 'Invalid event type' },
          { status: 400 }
        );
    }

    // Add rate limit headers to successful response
    const headers = createRateLimitHeaders(20, rateLimitResult.remaining, rateLimitResult.resetTime);

    return NextResponse.json({
      success: true,
      message: `Security event '${eventType}' logged successfully`,
      eventType,
      userId,
      description: eventDescription,
      ipAddress: finalIpAddress,
      timestamp: new Date().toISOString(),
    }, { headers });

  } catch (error) {
    console.error('[Security Event Test] Error:', error);
    return NextResponse.json(
      {
        error: 'Failed to log security event',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

/**
 * GET endpoint to retrieve available event types for testing
 */
export async function GET() {
  try {
    // Check authentication and admin role
    const session = await auth();
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    if (session.user.role !== 'admin') {
      return NextResponse.json(
        { error: 'Admin access required' },
        { status: 403 }
      );
    }

    return NextResponse.json({
      success: true,
      availableEventTypes: [
        'login',
        'password_change',
        '2fa_setup',
        '2fa_disable',
        'failed_login',
        'suspicious_activity'
      ],
      description: 'Security event testing endpoint for administrators',
      usage: {
        method: 'POST',
        body: {
          eventType: 'string (required)',
          description: 'string (optional)',
          targetUserId: 'string (optional, admin only)'
        }
      }
    });

  } catch (error) {
    console.error('[Security Event Test] GET Error:', error);
    return NextResponse.json(
      { error: 'Failed to retrieve event types' },
      { status: 500 }
    );
  }
}
