import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/lib/auth';
import { logSecurityEvent } from '@/lib/services/security-service';
import { getRequestMetadata } from '@/lib/utils/ip-utils';

/**
 * Test endpoint for security events (development only)
 * This endpoint allows testing the security event system
 */
export async function POST(request: NextRequest) {
  // Only allow in development
  if (process.env.NODE_ENV === 'production') {
    return NextResponse.json(
      { error: 'This endpoint is only available in development' },
      { status: 403 }
    );
  }

  try {
    const session = await auth();
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { eventType } = body;

    const userId = session.user.id;
    const { ipAddress, userAgent } = getRequestMetadata(request);

    // Fallback for testing if no IP detected
    const finalIpAddress = ipAddress || '127.0.0.1';
    const finalUserAgent = userAgent || 'Test User Agent';

    // Test different event types
    switch (eventType) {
      case 'login':
        await logSecurityEvent({
          userId,
          type: 'login',
          description: 'Test login event',
          ipAddress: finalIpAddress,
          userAgent: finalUserAgent,
        });
        break;

      case 'password_change':
        await logSecurityEvent({
          userId,
          type: 'password_change',
          description: 'Test password change event',
          ipAddress: finalIpAddress,
          userAgent: finalUserAgent,
        });
        break;

      case '2fa_setup':
        await logSecurityEvent({
          userId,
          type: '2fa_setup',
          description: 'Test 2FA setup event',
          ipAddress: finalIpAddress,
          userAgent: finalUserAgent,
        });
        break;

      case '2fa_disable':
        await logSecurityEvent({
          userId,
          type: '2fa_disable',
          description: 'Test 2FA disable event',
          ipAddress: finalIpAddress,
          userAgent: finalUserAgent,
        });
        break;

      case 'failed_login':
        await logSecurityEvent({
          userId,
          type: 'failed_login',
          description: 'Test failed login event',
          ipAddress: finalIpAddress,
          userAgent: finalUserAgent,
        });
        break;

      case 'suspicious_activity':
        await logSecurityEvent({
          userId,
          type: 'suspicious_activity',
          description: 'Test suspicious activity event',
          ipAddress: finalIpAddress,
          userAgent: finalUserAgent,
          // Location will be automatically resolved from IP address
        });
        break;

      default:
        return NextResponse.json(
          { error: 'Invalid event type. Use: login, password_change, 2fa_setup, 2fa_disable, failed_login, suspicious_activity' },
          { status: 400 }
        );
    }

    return NextResponse.json({
      success: true,
      message: `Test ${eventType} security event logged successfully`,
      eventType,
      userId,
    });

  } catch (error) {
    console.error('[Test Security Event] Error:', error);
    return NextResponse.json(
      { error: 'Failed to log test security event' },
      { status: 500 }
    );
  }
}
