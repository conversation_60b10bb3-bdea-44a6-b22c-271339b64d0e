import { NextRequest, NextResponse } from 'next/server';
import { getLocationFromIP } from '@/lib/services/security-service';
import { getRequestMetadata } from '@/lib/utils/ip-utils';

/**
 * Test endpoint for geolocation functionality (development only)
 * This endpoint allows testing the IP geolocation service
 */
export async function GET(request: NextRequest) {
  // Only allow in development
  if (process.env.NODE_ENV === 'production') {
    return NextResponse.json(
      { error: 'This endpoint is only available in development' },
      { status: 403 }
    );
  }

  try {
    const { ipAddress, userAgent } = getRequestMetadata(request);
    
    // Get IP from query parameter or use detected IP
    const testIP = request.nextUrl.searchParams.get('ip') || ipAddress || '127.0.0.1';
    
    console.log(`[Geolocation Test] Testing IP: ${testIP}`);
    
    // Test geolocation
    const location = await getLocationFromIP(testIP);
    
    return NextResponse.json({
      success: true,
      testIP,
      detectedIP: ipAddress,
      userAgent,
      location,
      timestamp: new Date().toISOString(),
    });

  } catch (error) {
    console.error('[Geolocation Test] Error:', error);
    return NextResponse.json(
      { 
        error: 'Failed to test geolocation',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

/**
 * Test endpoint with POST to test specific IPs
 */
export async function POST(request: NextRequest) {
  // Only allow in development
  if (process.env.NODE_ENV === 'production') {
    return NextResponse.json(
      { error: 'This endpoint is only available in development' },
      { status: 403 }
    );
  }

  try {
    const body = await request.json();
    const { ip } = body;

    if (!ip) {
      return NextResponse.json(
        { error: 'IP address is required in request body' },
        { status: 400 }
      );
    }

    console.log(`[Geolocation Test] Testing IP: ${ip}`);
    
    // Test geolocation
    const location = await getLocationFromIP(ip);
    
    return NextResponse.json({
      success: true,
      testIP: ip,
      location,
      timestamp: new Date().toISOString(),
    });

  } catch (error) {
    console.error('[Geolocation Test] Error:', error);
    return NextResponse.json(
      { 
        error: 'Failed to test geolocation',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
