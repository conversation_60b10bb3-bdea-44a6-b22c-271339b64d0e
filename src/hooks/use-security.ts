import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { toast } from 'sonner';
import { queryKeys } from '@/lib/query-keys';
import { apiGet, apiPatch } from '@/lib/api-client';

export interface SecuritySettings {
  requireTwoFactor: boolean;
  emailSecurityAlerts: boolean;
  loginAlerts: boolean;
  passwordChangeAlerts: boolean;
  twoFactorAlerts: boolean;
  suspiciousActivityAlerts: boolean;
  twoFactorEnabled: boolean;
  lastPasswordChange?: Date;
  accountCreated: Date;
  lastLogin?: Date;
  emailVerified: boolean;
  hasPassword: boolean;
  connectedAccounts: {
    provider: string;
    connectedAt: Date;
  }[];
}

/**
 * Fetch security settings
 */
async function fetchSecuritySettings(): Promise<SecuritySettings> {
  const data = await apiGet<{
    requireTwoFactor: boolean;
    emailSecurityAlerts: boolean;
    loginAlerts: boolean;
    passwordChangeAlerts: boolean;
    twoFactorAlerts: boolean;
    suspiciousActivityAlerts: boolean;
    twoFactorEnabled: boolean;
    lastPasswordChange?: string;
    accountCreated: string;
    lastLogin?: string;
    emailVerified: boolean;
    hasPassword: boolean;
    connectedAccounts: {
      provider: string;
      connectedAt: string;
    }[];
  }>('/api/user/security');

  // Convert date strings to Date objects
  return {
    ...data,
    lastPasswordChange: data.lastPasswordChange ? new Date(data.lastPasswordChange) : undefined,
    accountCreated: new Date(data.accountCreated),
    lastLogin: data.lastLogin ? new Date(data.lastLogin) : undefined,
    connectedAccounts: data.connectedAccounts.map((account) => ({
      ...account,
      connectedAt: new Date(account.connectedAt),
    })),
  };
}

/**
 * Update security settings
 */
async function updateSecuritySettings(updates: Partial<SecuritySettings>): Promise<void> {
  await apiPatch<void>('/api/user/security', updates);
}

/**
 * Hook to fetch security settings
 */
export function useSecuritySettings() {
  return useQuery({
    queryKey: queryKeys.users.security(),
    queryFn: fetchSecuritySettings,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

/**
 * Hook to update security settings
 */
export function useUpdateSecuritySettings() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: updateSecuritySettings,
    onSuccess: () => {
      // Invalidate and refetch security settings
      queryClient.invalidateQueries({ queryKey: queryKeys.users.security() });
      toast.success('Security settings updated successfully');
    },
    onError: (error: Error) => {
      toast.error(error.message || 'Failed to update security settings');
    },
  });
}
